/**
 * WCAG API Service
 * Frontend service for WCAG API communication
 */

import axios, { AxiosResponse } from 'axios';
import {
  WcagScanConfig,
  WcagScanResult,
  WcagScanFormData,
  ScanProgressInfo,
  QueueStatusInfo,
} from '../types/wcag';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  requestId: string;
  processingTime: number;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
    context?: string;
  };
  requestId: string;
  processingTime: number;
}

export interface ScanListResponse {
  scans: Array<{
    scanId: string;
    targetUrl: string;
    status: string;
    overallScore?: number;
    levelAchieved?: string;
    riskLevel?: string;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number; // Count only, no scoring
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

class WcagApiService {
  private baseURL: string;
  private authToken: string | null = null;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_BACKEND_API_URL ||
                   process.env.NEXT_PUBLIC_API_BASE_URL ||
                   'http://localhost:3001/api/v1';
  }

  /**
   * Set authentication token
   */
  setAuthToken(token: string): void {
    this.authToken = token;
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = null;
    delete axios.defaults.headers.common['Authorization'];
  }

  /**
   * Start a new WCAG scan
   */
  async startScan(formData: WcagScanFormData): Promise<WcagScanResult> {
    try {
      const scanConfig: WcagScanConfig = {
        targetUrl: formData.targetUrl,
        scanOptions: {
          enableContrastAnalysis: formData.enableContrastAnalysis,
          enableKeyboardTesting: formData.enableKeyboardTesting,
          enableFocusAnalysis: formData.enableFocusAnalysis,
          enableSemanticValidation: formData.enableSemanticValidation,
          wcagVersion: formData.wcagVersion,
          level: formData.level,
          maxPages: formData.maxPages,
        },
        userId: '', // Will be set by backend from auth token
        requestId: '', // Will be generated by backend
      };

      const response: AxiosResponse<ApiResponse<WcagScanResult>> = await axios.post(
        `${this.baseURL}/compliance/wcag/scan`,
        scanConfig,
      );

      if (!response.data.success) {
        throw new Error('Scan request failed');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to start WCAG scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get list of user's scans
   */
  async getScans(
    params: {
      page?: number;
      limit?: number;
      status?: string;
      sortBy?: string;
      sortOrder?: string;
    } = {},
  ): Promise<ScanListResponse> {
    try {
      const response: AxiosResponse<ApiResponse<ScanListResponse>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans`,
        { params },
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch scans');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch WCAG scans:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get detailed scan results
   */
  async getScanDetails(scanId: string): Promise<WcagScanResult> {
    try {
      const response: AxiosResponse<ApiResponse<WcagScanResult>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans/${scanId}`,
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch scan details');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch scan details:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Delete a scan
   */
  async deleteScan(scanId: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<{ message: string }>> = await axios.delete(
        `${this.baseURL}/compliance/wcag/scans/${scanId}`,
      );

      if (!response.data.success) {
        throw new Error('Failed to delete scan');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to delete scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Export scan results
   */
  async exportScan(
    scanId: string,
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence?: boolean;
      includeRecommendations?: boolean;
      includeManualReviewItems?: boolean; // Include manual review tracking
    } = {},
  ): Promise<Blob> {
    try {
      const response = await axios.post(
        `${this.baseURL}/compliance/wcag/export`,
        {
          scanId,
          format,
          ...options,
        },
        {
          responseType: 'blob',
        },
      );

      return response.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to export scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Download exported file
   */
  async downloadExport(
    scanId: string,
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence?: boolean;
      includeRecommendations?: boolean;
      includeManualReviewItems?: boolean;
    } = {},
  ): Promise<void> {
    try {
      const blob = await this.exportScan(scanId, format, options);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Set filename based on format
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `wcag-${format === 'pdf' ? 'report' : 'data'}-${scanId.slice(0, 8)}-${timestamp}.${format}`;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to download export:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get scan progress (for real-time updates)
   */
  async getScanProgress(scanId: string): Promise<ScanProgressInfo | null> {
    try {
      // This would typically use WebSocket or Server-Sent Events
      // For now, implement as polling endpoint
      const response: AxiosResponse<ApiResponse<ScanProgressInfo | null>> = await axios.get(
        `${this.baseURL}/compliance/wcag/scans/${scanId}/progress`,
      );

      if (!response.data.success) {
        return null;
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch scan progress:', error);
      return null;
    }
  }

  /**
   * Get queue status
   */
  async getQueueStatus(): Promise<QueueStatusInfo> {
    try {
      const response: AxiosResponse<ApiResponse<QueueStatusInfo>> = await axios.get(
        `${this.baseURL}/compliance/wcag/queue/status`,
      );

      if (!response.data.success) {
        throw new Error('Failed to fetch queue status');
      }

      return response.data.data;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to fetch queue status:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Cancel a scan
   */
  async cancelScan(scanId: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<{ message: string }>> = await axios.post(
        `${this.baseURL}/compliance/wcag/scans/${scanId}/cancel`,
      );

      if (!response.data.success) {
        throw new Error('Failed to cancel scan');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to cancel scan:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Check API health
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseURL}/compliance/wcag/health`);
      return response.data.success;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Health check failed:', error);
      return false;
    }
  }

  /**
   * Handle API errors consistently
   */
  private handleApiError(
    error: unknown /* eslint-disable-line @typescript-eslint/no-explicit-any */,
  ): Error {
    if (axios.isAxiosError(error)) {
      if (error.response?.data?.error) {
        const apiError = error.response.data as ApiError;
        return new Error(apiError.error.message || 'API request failed');
      }

      if (error.response?.status === 401) {
        return new Error('Authentication required - please log in');
      }

      if (error.response?.status === 403) {
        return new Error('Insufficient permissions for WCAG scanning');
      }

      if (error.response?.status === 429) {
        return new Error('Rate limit exceeded - please wait before making more requests');
      }

      return new Error(error.message || 'Network error occurred');
    }

    return error instanceof Error ? error : new Error('Unknown error occurred');
  }
}

// Export singleton instance
export const wcagApiService = new WcagApiService();
export default wcagApiService;
