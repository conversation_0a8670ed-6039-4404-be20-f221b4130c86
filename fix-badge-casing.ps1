# Script to fix remaining Badge casing issues
$projectRoot = "D:\Web projects\Comply Checker"

# Files with Badge casing issues
$filesToFix = @(
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\hipaa\privacy\[scanId]\page.tsx"
        "OldImport" = "@/components/ui/Badge"
        "NewImport" = "@/components/ui/badge"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\hipaa\security\[scanId]\page.tsx"
        "OldImport" = "@/components/ui/Badge"
        "NewImport" = "@/components/ui/badge"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\scans\[scanId]\page.tsx"
        "OldImport" = "@/components/ui/Badge"
        "NewImport" = "@/components/ui/badge"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\scans\[scanId]\findings\page.tsx"
        "OldImport" = "@/components/ui/Badge"
        "NewImport" = "@/components/ui/badge"
    }
)

foreach ($file in $filesToFix) {
    if (Test-Path $file.Path) {
        $content = Get-Content $file.Path -Raw
        
        if ($content -match $file.OldImport) {
            Write-Host "Fixing $($file.OldImport) in $($file.Path)"
            $content = $content -replace $file.OldImport, $file.NewImport
            Set-Content -Path $file.Path -Value $content
        }
    }
}

# Fix the WcagScanOverview import issue
$wcagScanOverviewPath = Join-Path $projectRoot "frontend\app\dashboard\wcag\scan\[scanId]\page.tsx"
if (Test-Path $wcagScanOverviewPath) {
    $content = Get-Content $wcagScanOverviewPath -Raw
    if ($content -match "import \{ WcagScanOverview \} from") {
        Write-Host "Fixing WcagScanOverview import in $wcagScanOverviewPath"
        $content = $content -replace "import \{ WcagScanOverview \} from '@/components/wcag/WcagScanOverview';", "import WcagScanOverview from '@/components/wcag/WcagScanOverview';"
        Set-Content -Path $wcagScanOverviewPath -Value $content
    }
}

# Fix lucide-react Assessment icon issue
$wcagLayoutPath = Join-Path $projectRoot "frontend\app\dashboard\wcag\layout.tsx"
if (Test-Path $wcagLayoutPath) {
    $content = Get-Content $wcagLayoutPath -Raw
    if ($content -match "Assessment") {
        Write-Host "Fixing Assessment icon in $wcagLayoutPath"
        $content = $content -replace "Assessment", "ClipboardCheck"
        Set-Content -Path $wcagLayoutPath -Value $content
    }
}

Write-Host "Badge casing and other fixes completed!"
