$projectRoot = "D:\Web projects\Comply Checker\frontend"

# Components to fix (based on error messages)
$componentsToFix = @(
    # Absolute imports
    @{
        "OldImport" = "@/components/ui/Card";
        "NewImport" = "@/components/ui/card";
        "FileName" = "Card.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Button";
        "NewImport" = "@/components/ui/button";
        "FileName" = "Button.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Badge";
        "NewImport" = "@/components/ui/badge";
        "FileName" = "Badge.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Progress";
        "NewImport" = "@/components/ui/progress";
        "FileName" = "Progress.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Checkbox";
        "NewImport" = "@/components/ui/checkbox";
        "FileName" = "Checkbox.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Alert";
        "NewImport" = "@/components/ui/alert";
        "FileName" = "Alert.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Tabs";
        "NewImport" = "@/components/ui/tabs";
        "FileName" = "Tabs.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Label";
        "NewImport" = "@/components/ui/label";
        "FileName" = "Label.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Input";
        "NewImport" = "@/components/ui/input";
        "FileName" = "Input.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Table";
        "NewImport" = "@/components/ui/table";
        "FileName" = "Table.tsx"
    },
    @{
        "OldImport" = "@/components/ui/Tooltip";
        "NewImport" = "@/components/ui/tooltip";
        "FileName" = "Tooltip.tsx"
    },
    
    # Relative imports from same directory
    @{
        "OldImport" = "./Card";
        "NewImport" = "./card";
        "FileName" = "Card.tsx"
    },
    @{
        "OldImport" = "./Button";
        "NewImport" = "./button";
        "FileName" = "Button.tsx"
    },
    @{
        "OldImport" = "./Alert";
        "NewImport" = "./alert";
        "FileName" = "Alert.tsx"
    },
    @{
        "OldImport" = "./Input";
        "NewImport" = "./input";
        "FileName" = "Input.tsx"
    },
    @{
        "OldImport" = "./Label";
        "NewImport" = "./label";
        "FileName" = "Label.tsx"
    },
    @{
        "OldImport" = "./Progress";
        "NewImport" = "./progress";
        "FileName" = "Progress.tsx"
    },
    @{
        "OldImport" = "./Checkbox";
        "NewImport" = "./checkbox";
        "FileName" = "Checkbox.tsx"
    },
    @{
        "OldImport" = "./Tabs";
        "NewImport" = "./tabs";
        "FileName" = "Tabs.tsx"
    },
    
    # Relative imports from parent directory
    @{
        "OldImport" = "../ui/Card";
        "NewImport" = "../ui/card";
        "FileName" = "Card.tsx"
    },
    @{
        "OldImport" = "../ui/Button";
        "NewImport" = "../ui/button";
        "FileName" = "Button.tsx"
    },
    @{
        "OldImport" = "../ui/Progress";
        "NewImport" = "../ui/progress";
        "FileName" = "Progress.tsx"
    },
    @{
        "OldImport" = "../ui/Checkbox";
        "NewImport" = "../ui/checkbox";
        "FileName" = "Checkbox.tsx"
    },
    @{
        "OldImport" = "../ui/Alert";
        "NewImport" = "../ui/alert";
        "FileName" = "Alert.tsx"
    },
    @{
        "OldImport" = "../ui/Input";
        "NewImport" = "../ui/input";
        "FileName" = "Input.tsx"
    },
    @{
        "OldImport" = "../ui/Label";
        "NewImport" = "../ui/label";
        "FileName" = "Label.tsx"
    },
    @{
        "OldImport" = "../ui/Tabs";
        "NewImport" = "../ui/tabs";
        "FileName" = "Tabs.tsx"
    }
)

# Find all TypeScript and TSX files in the project
$files = Get-ChildItem -Path $projectRoot -Recurse -Include "*.ts", "*.tsx"

# Process each file
foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $modified = $false
    
    foreach ($component in $componentsToFix) {
        # Check if the file contains the import
        if ($content -match [regex]::Escape($component.OldImport)) {
            Write-Host "Fixing $($component.OldImport) in $($file.FullName)"
            $content = $content -replace [regex]::Escape($component.OldImport), $component.NewImport
            $modified = $true
        }
    }
    
    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content
        Write-Host "Updated $($file.FullName)"
    }
}

# Rename the UI component files to lowercase to match imports
$uiComponentsDir = Join-Path -Path $projectRoot -ChildPath "components\ui"
foreach ($component in $componentsToFix) {
    $filePath = Join-Path -Path $uiComponentsDir -ChildPath $component.FileName
    if (Test-Path $filePath) {
        $newFileName = $component.FileName.ToLower()
        $newFilePath = Join-Path -Path $uiComponentsDir -ChildPath $newFileName
        
        # Check if the lowercase file already exists
        if (-not (Test-Path $newFilePath)) {
            Write-Host "Renaming $filePath to $newFilePath"
            # Uncomment the line below to actually perform the rename
            # Rename-Item -Path $filePath -NewName $newFileName
        }
    }
}

Write-Host "Import casing fixes completed!"
