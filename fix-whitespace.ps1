# Script to fix whitespace issues in specific files
$filePath = "D:\Web projects\Comply Checker\frontend\app\dashboard\wcag\history\page.tsx"

# Read the file content
$content = Get-Content -Path $filePath -Raw

# Replace trailing whitespace
$content = $content -replace " +`r`n", "`r`n"
$content = $content -replace " +`n", "`n"

# Write the content back to the file
Set-Content -Path $filePath -Value $content -NoNewline

Write-Host "Fixed whitespace issues in $filePath"
