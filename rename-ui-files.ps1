# Script to rename UI component files to lowercase
$projectRoot = "D:\Web projects\Comply Checker"
$uiComponentsDir = Join-Path $projectRoot "frontend\components\ui"

# Components to rename (based on error messages)
$componentsToRename = @(
    @{
        "OldName" = "Alert.tsx"
        "NewName" = "alert.tsx"
    },
    @{
        "OldName" = "Input.tsx"
        "NewName" = "input.tsx"
    },
    @{
        "OldName" = "Label.tsx"
        "NewName" = "label.tsx"
    },
    @{
        "OldName" = "Checkbox.tsx"
        "NewName" = "checkbox.tsx"
    },
    @{
        "OldName" = "Button.tsx"
        "NewName" = "button.tsx"
    },
    @{
        "OldName" = "Card.tsx"
        "NewName" = "card.tsx"
    },
    @{
        "OldName" = "Badge.tsx"
        "NewName" = "badge.tsx"
    },
    @{
        "OldName" = "Progress.tsx"
        "NewName" = "progress.tsx"
    },
    @{
        "OldName" = "Tabs.tsx"
        "NewName" = "tabs.tsx"
    },
    @{
        "OldName" = "Table.tsx"
        "NewName" = "table.tsx"
    },
    @{
        "OldName" = "Tooltip.tsx"
        "NewName" = "tooltip.tsx"
    }
)

# Rename the files
foreach ($component in $componentsToRename) {
    $oldPath = Join-Path $uiComponentsDir $component.OldName
    $newPath = Join-Path $uiComponentsDir $component.NewName
    
    if (Test-Path $oldPath) {
        Write-Host "Renaming $($component.OldName) to $($component.NewName)"
        
        # Windows is case-insensitive, so we need to use a temporary name first
        $tempPath = Join-Path $uiComponentsDir "temp_$($component.NewName)"
        Rename-Item -Path $oldPath -NewName $tempPath
        Rename-Item -Path $tempPath -NewName $component.NewName
    } else {
        Write-Host "File $($component.OldName) not found"
    }
}

Write-Host "UI component files renamed successfully!"
