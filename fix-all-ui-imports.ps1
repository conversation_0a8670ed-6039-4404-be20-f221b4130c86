# Script to fix all UI component imports to use consistent lowercase paths
# This addresses the Windows filesystem casing sensitivity issues in TypeScript

$uiComponents = @(
    "accordion",
    "alert",
    "avatar",
    "badge",
    "button",
    "calendar",
    "card",
    "checkbox",
    "command",
    "dialog",
    "dropdown-menu",
    "form",
    "hover-card",
    "input",
    "label",
    "menubar",
    "navigation-menu",
    "popover",
    "progress",
    "radio-group",
    "scroll-area",
    "select",
    "separator",
    "sheet",
    "skeleton",
    "slider",
    "switch",
    "table",
    "tabs",
    "textarea",
    "toast",
    "toggle",
    "tooltip"
)

$frontendDir = "D:\Web projects\Comply Checker\frontend"

Write-Host "Fixing UI component imports to use consistent lowercase paths..."

# Find all TypeScript and TSX files in the frontend directory
$files = Get-ChildItem -Path $frontendDir -Include "*.ts", "*.tsx" -Recurse

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $modified = $false

    foreach ($component in $uiComponents) {
        # Fix PascalCase imports (e.g., @/components/ui/Button -> @/components/ui/button)
        $pascalComponent = (Get-Culture).TextInfo.ToTitleCase($component)
        
        # Fix imports with single quotes
        if ($content -match "@/components/ui/$pascalComponent'") {
            $content = $content -replace "@/components/ui/$pascalComponent'", "@/components/ui/$component'"
            $modified = $true
        }
        
        # Fix imports with double quotes
        if ($content -match "@/components/ui/$pascalComponent`"") {
            $content = $content -replace "@/components/ui/$pascalComponent`"", "@/components/ui/$component`""
            $modified = $true
        }
        
        # Fix relative imports with single quotes
        if ($content -match "\.\./ui/$pascalComponent'") {
            $content = $content -replace "\.\./ui/$pascalComponent'", "../ui/$component'"
            $modified = $true
        }
        
        # Fix relative imports with double quotes
        if ($content -match "\.\./ui/$pascalComponent`"") {
            $content = $content -replace "\.\./ui/$pascalComponent`"", "../ui/$component`""
            $modified = $true
        }
        
        # Fix relative imports with single quotes (same directory)
        if ($content -match "\./ui/$pascalComponent'") {
            $content = $content -replace "\./ui/$pascalComponent'", "./ui/$component'"
            $modified = $true
        }
        
        # Fix relative imports with double quotes (same directory)
        if ($content -match "\./ui/$pascalComponent`"") {
            $content = $content -replace "\./ui/$pascalComponent`"", "./ui/$component`""
            $modified = $true
        }
        
        # Fix direct imports with single quotes
        if ($content -match "\.\/$pascalComponent'") {
            $content = $content -replace "\.\/$pascalComponent'", "./$component'"
            $modified = $true
        }
        
        # Fix direct imports with double quotes
        if ($content -match "\.\/$pascalComponent`"") {
            $content = $content -replace "\.\/$pascalComponent`"", "./$component`""
            $modified = $true
        }
    }

    # Save changes if the file was modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content
        Write-Host "Fixed imports in $($file.FullName)"
    }
}

Write-Host "All UI component imports have been fixed to use consistent lowercase paths."
