/**
 * WCAG Test Page Component
 * Demonstration page showing all WCAG components working together
 */

'use client';

import React, { useState } from 'react';
import { WcagScanForm, WcagScanOverview, WcagScanProgress, WcagExportDialog } from './index';
import { WcagScanFormData, WcagScanResult } from '../../types/wcag';
import wcagApiService from '../../services/wcag-api';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';

const WcagTestPage: React.FC = () => {
  const [currentScan, setCurrentScan] = useState<WcagScanResult | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('form');
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportError, setExportError] = useState<string>('');

  /**
   * Handle scan form submission
   */
  const handleScanSubmit = async (formData: WcagScanFormData) => {
    try {
      setError('');
      setIsScanning(true);
      setActiveTab('progress');

      // Start the scan
      const scanResult = await wcagApiService.startScan(formData);
      setCurrentScan(scanResult);

      // If scan is already completed (unlikely but possible)
      if (scanResult.status === 'completed') {
        setIsScanning(false);
        setActiveTab('results');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start scan');
      setIsScanning(false);
      setActiveTab('form');
    }
  };

  /**
   * Handle scan completion
   */
  const handleScanComplete = async () => {
    if (currentScan) {
      try {
        // Fetch final scan results
        const finalResults = await wcagApiService.getScanDetails(currentScan.scanId);
        setCurrentScan(finalResults);
        setIsScanning(false);
        setActiveTab('results');
      } catch (err) {
        setError('Failed to fetch final scan results');
        setIsScanning(false);
      }
    }
  };

  /**
   * Handle scan cancellation
   */
  const handleScanCancel = () => {
    setCurrentScan(null);
    setIsScanning(false);
    setActiveTab('form');
  };

  /**
   * Create mock scan result for testing
   */
  const createMockScanResult = (): WcagScanResult => {
    return {
      scanId: 'test-scan-123',
      targetUrl: 'https://example.com',
      status: 'completed',
      overallScore: 85,
      levelAchieved: 'AA',
      riskLevel: 'medium',
      summary: {
        totalAutomatedChecks: 21,
        passedAutomatedChecks: 18,
        failedAutomatedChecks: 3,
        automatedScore: 85,
        automationRate: 0.87,
        manualReviewItems: 5,
        categoryScores: {
          perceivable: 88,
          operable: 82,
          understandable: 90,
          robust: 80,
        },
        versionScores: {
          wcag21: 85,
          wcag22: 88,
          wcag30: 82,
        },
      },
      metadata: {
        scanId: 'test-scan-123',
        userId: 'test-user',
        requestId: 'test-request-123',
        startTime: new Date(),
        duration: 45000,
        userAgent: 'Test Browser',
        viewport: { width: 1920, height: 1080 },
        environment: 'test',
        version: '1.0.0',
      },
      checks: [],
      recommendations: [],
      // Add missing required properties
      url: 'https://example.com', // Alias for targetUrl
      scanTimestamp: new Date().toISOString(),
      wcagVersion: 'all',
      complianceLevel: 'AA',
      scanOptions: {
        maxPages: 5,
        enableContrastAnalysis: true,
        enableKeyboardTesting: true,
        enableFocusAnalysis: true,
        enableSemanticValidation: true,
        wcagVersion: 'all',
        level: 'AA',
      },
    };
  };

  /**
   * Load mock data for testing
   */
  const loadMockData = () => {
    const mockResult = createMockScanResult();
    setCurrentScan(mockResult);
    setIsScanning(false);
    setActiveTab('results');
  };

  /**
   * Handle export button click
   */
  const handleExportClick = () => {
    setExportError('');
    setExportDialogOpen(true);
  };

  /**
   * Handle export execution
   */
  const handleExport = async (
    format: 'pdf' | 'json' | 'csv',
    options: {
      includeEvidence: boolean;
      includeRecommendations: boolean;
      includeManualReviewItems: boolean;
    },
  ) => {
    if (!currentScan) return;

    try {
      setIsExporting(true);
      setExportError('');

      // Use the download export function which handles file download
      await wcagApiService.downloadExport(currentScan.scanId, format, options);
    } catch (err) {
      setExportError(err instanceof Error ? err.message : 'Export failed');
      throw err; // Re-throw to let dialog handle it
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>WCAG Components Test Page</CardTitle>
          <p className="text-sm text-muted-foreground">
            Test and demonstrate WCAG compliance scanning components
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <button
              onClick={loadMockData}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Load Mock Results
            </button>
            <button
              onClick={() => {
                setCurrentScan(null);
                setIsScanning(false);
                setActiveTab('form');
                setError('');
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Reset
            </button>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="form">Scan Form</TabsTrigger>
              <TabsTrigger value="progress" disabled={!isScanning && !currentScan}>
                Progress
              </TabsTrigger>
              <TabsTrigger
                value="results"
                disabled={!currentScan || currentScan.status !== 'completed'}
              >
                Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="form" className="mt-6">
              <WcagScanForm onSubmit={handleScanSubmit} isLoading={isScanning} error={error} />
            </TabsContent>

            <TabsContent value="progress" className="mt-6">
              {currentScan && (
                <WcagScanProgress
                  scanId={currentScan.scanId}
                  onComplete={handleScanComplete}
                  onCancel={handleScanCancel}
                />
              )}
            </TabsContent>

            <TabsContent value="results" className="mt-6">
              {currentScan && currentScan.status === 'completed' && (
                <WcagScanOverview scanResult={currentScan} onExport={handleExportClick} loading={isExporting} />
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Export Dialog */}
      {currentScan && (
        <WcagExportDialog
          open={exportDialogOpen}
          onClose={() => setExportDialogOpen(false)}
          onExport={handleExport}
          scanId={currentScan.scanId}
          isLoading={isExporting}
          error={exportError}
        />
      )}
    </div>
  );
};

export default WcagTestPage;
