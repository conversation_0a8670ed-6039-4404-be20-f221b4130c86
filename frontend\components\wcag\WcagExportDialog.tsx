/**
 * WCAG Export Dialog Component
 * Dialog for configuring and initiating exports
 */

'use client';

import React, { useState } from 'react';
import { Download, X } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Alert } from '../ui/alert';

interface WcagExportDialogProps {
  open: boolean;
  onClose: () => void;
  onExport: (format: 'pdf' | 'json' | 'csv', options: ExportOptions) => Promise<void>;
  scanId: string;
  isLoading?: boolean;
  error?: string;
}

interface ExportOptions {
  includeEvidence: boolean;
  includeRecommendations: boolean;
  includeManualReviewItems: boolean; // Match backend schema
}

const WcagExportDialog: React.FC<WcagExportDialogProps> = ({
  open,
  onClose,
  onExport,
  scanId,
  isLoading = false,
  error
}) => {
  const [format, setFormat] = useState<'pdf' | 'json' | 'csv'>('pdf');
  const [options, setOptions] = useState<ExportOptions>({
    includeEvidence: true,
    includeRecommendations: true,
    includeManualReviewItems: true
  });

  if (!open) return null;

  /**
   * Handle format change
   */
  const handleFormatChange = (newFormat: 'pdf' | 'json' | 'csv') => {
    setFormat(newFormat);
  };

  /**
   * Handle option change
   */
  const handleOptionChange = (option: keyof ExportOptions, checked: boolean) => {
    setOptions(prev => ({
      ...prev,
      [option]: checked
    }));
  };

  /**
   * Handle export
   */
  const handleExport = async () => {
    try {
      await onExport(format, options);
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  /**
   * Get format description
   */
  const getFormatDescription = (format: string): string => {
    switch (format) {
      case 'pdf':
        return 'Professional report with detailed analysis';
      case 'json':
        return 'Complete data export for integration with other tools';
      case 'csv':
        return 'Spreadsheet-compatible format for data analysis';
      default:
        return '';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-md mx-4">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Export WCAG Report
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {error && (
            <Alert>
              {error}
            </Alert>
          )}
          
          <p className="text-sm text-gray-600">
            Export your WCAG compliance scan results in your preferred format
          </p>

          {/* Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Export Format</Label>
            
            <div className="space-y-2">
              {(['pdf', 'json', 'csv'] as const).map((formatOption) => (
                <div key={formatOption} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id={formatOption}
                    name="format"
                    checked={format === formatOption}
                    onChange={() => handleFormatChange(formatOption)}
                    className="h-4 w-4"
                  />
                  <div className="flex-1">
                    <Label htmlFor={formatOption} className="text-sm font-medium">
                      {formatOption.toUpperCase()} {formatOption === 'pdf' ? 'Report' : 'Data'}
                    </Label>
                    <p className="text-xs text-gray-500">
                      {getFormatDescription(formatOption)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Export Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Include in Export</Label>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeEvidence"
                  checked={options.includeEvidence}
                  onCheckedChange={(checked) => handleOptionChange('includeEvidence', checked as boolean)}
                />
                <Label htmlFor="includeEvidence" className="text-sm">
                  Evidence and Screenshots
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeRecommendations"
                  checked={options.includeRecommendations}
                  onCheckedChange={(checked) => handleOptionChange('includeRecommendations', checked as boolean)}
                />
                <Label htmlFor="includeRecommendations" className="text-sm">
                  Recommendations
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeManualReviewItems"
                  checked={options.includeManualReviewItems}
                  onCheckedChange={(checked) => handleOptionChange('includeManualReviewItems', checked as boolean)}
                />
                <Label htmlFor="includeManualReviewItems" className="text-sm">
                  Manual Review Items
                </Label>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={isLoading}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WcagExportDialog;
