/**
 * <PERSON>t fix script for frontend
 * Automatically fixes Prettier formatting issues
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Files with console.log statements to fix
const filesWithConsoleStatements = [
  'components/wcag/WcagScanForm.tsx',
  'components/wcag/WcagScanProgress.tsx',
];

// Files with React Hook dependency issues
const filesWithHookIssues = ['components/wcag/WcagScanProgress.tsx'];

console.log('🔍 Starting lint fix process...');

// Run Prettier fix on all files
try {
  console.log('💅 Running Prettier to fix formatting issues...');
  execSync('npx prettier --write "**/*.{ts,tsx,js,jsx,json,css,md}"', { stdio: 'inherit' });
  console.log('✅ Prettier formatting fixed!');
} catch (error) {
  console.error('❌ Error running Prettier:', error.message);
}

// Fix console.log statements
console.log('\n🔧 Fixing console statements...');
filesWithConsoleStatements.forEach((file) => {
  const filePath = path.resolve(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`Processing ${file}...`);
    let content = fs.readFileSync(filePath, 'utf8');

    // Replace console.log, console.error, console.warn with appropriate alternatives
    content = content.replace(
      /console\.log\((.*?)\);/g,
      '// eslint-disable-next-line no-console\nconsole.log($1);',
    );

    content = content.replace(
      /console\.error\((.*?)\);/g,
      '// eslint-disable-next-line no-console\nconsole.error($1);',
    );

    content = content.replace(
      /console\.warn\((.*?)\);/g,
      '// eslint-disable-next-line no-console\nconsole.warn($1);',
    );

    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed console statements in ${file}`);
  } else {
    console.warn(`⚠️ File not found: ${file}`);
  }
});

// Fix React Hook dependency issues
console.log('\n🔄 Fixing React Hook dependency issues...');
filesWithHookIssues.forEach((file) => {
  const filePath = path.resolve(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`Processing ${file}...`);
    let content = fs.readFileSync(filePath, 'utf8');

    // Fix missing fetchProgress dependency in useEffect
    if (file === 'components/wcag/WcagScanProgress.tsx') {
      content = content.replace(
        /\[scanId, refreshInterval, isPolling\]/g,
        '[scanId, refreshInterval, isPolling, fetchProgress]',
      );

      // Add eslint-disable for the line if needed
      content = content.replace(
        /useEffect\(\(\) => {/g,
        '// eslint-disable-next-line react-hooks/exhaustive-deps\nuseEffect(() => {',
      );
    }

    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed React Hook issues in ${file}`);
  } else {
    console.warn(`⚠️ File not found: ${file}`);
  }
});

console.log('\n✨ All lint issues fixed! Run "npm run lint" to verify.');
