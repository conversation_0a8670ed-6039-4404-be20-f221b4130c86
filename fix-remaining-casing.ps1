# Script to fix remaining UI component casing issues
$projectRoot = "D:\Web projects\Comply Checker"
$uiComponentsDir = Join-Path $projectRoot "frontend\components\ui"

# 1. Rename Select.tsx and Switch.tsx to lowercase
$componentsToRename = @(
    @{
        "OldName" = "Select.tsx"
        "NewName" = "select.tsx"
    },
    @{
        "OldName" = "Switch.tsx"
        "NewName" = "switch.tsx"
    }
)

# Rename the files
foreach ($component in $componentsToRename) {
    $oldPath = Join-Path $uiComponentsDir $component.OldName
    $newPath = Join-Path $uiComponentsDir $component.NewName
    
    if (Test-Path $oldPath) {
        Write-Host "Renaming $($component.OldName) to $($component.NewName)"
        
        # Windows is case-insensitive, so we need to use a temporary name first
        $tempPath = Join-Path $uiComponentsDir "temp_$($component.NewName)"
        Rename-Item -Path $oldPath -NewName $tempPath
        Rename-Item -Path $tempPath -NewName $component.NewName
    } else {
        Write-Host "File $($component.OldName) not found"
    }
}

# 2. Fix imports in files that use Select and Switch with uppercase
$filesToFix = @(
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\wcag\reports\page.tsx"
        "OldImport" = "@/components/ui/Select"
        "NewImport" = "@/components/ui/select"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\wcag\settings\page.tsx"
        "OldImport" = "@/components/ui/Select"
        "NewImport" = "@/components/ui/select"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\wcag\settings\page.tsx"
        "OldImport" = "@/components/ui/Switch"
        "NewImport" = "@/components/ui/switch"
    }
)

foreach ($file in $filesToFix) {
    if (Test-Path $file.Path) {
        $content = Get-Content $file.Path -Raw
        
        if ($content -match $file.OldImport) {
            Write-Host "Fixing $($file.OldImport) in $($file.Path)"
            $content = $content -replace $file.OldImport, $file.NewImport
            Set-Content -Path $file.Path -Value $content
        }
    }
}

# 3. Fix WCAG component import issues
$wcagComponentFixes = @(
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\wcag\scan\[scanId]\page.tsx"
        "OldImport" = "import { WcagScanOverview } from '@/components/wcag/WcagScanOverview';"
        "NewImport" = "import WcagScanOverview from '@/components/wcag/WcagScanOverview';"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\wcag\scan\page.tsx"
        "OldImport" = "import { WcagScanForm } from '@/components/wcag/WcagScanForm';"
        "NewImport" = "import WcagScanForm from '@/components/wcag/WcagScanForm';"
    },
    @{
        "Path" = Join-Path $projectRoot "frontend\app\dashboard\wcag\scan\page.tsx"
        "OldImport" = "import { WcagScanProgress } from '@/components/wcag/WcagScanProgress';"
        "NewImport" = "import WcagScanProgress from '@/components/wcag/WcagScanProgress';"
    }
)

foreach ($fix in $wcagComponentFixes) {
    if (Test-Path $fix.Path) {
        $content = Get-Content $fix.Path -Raw
        
        if ($content -match [regex]::Escape($fix.OldImport)) {
            Write-Host "Fixing import in $($fix.Path)"
            $content = $content -replace [regex]::Escape($fix.OldImport), $fix.NewImport
            Set-Content -Path $fix.Path -Value $content
        }
    }
}

Write-Host "All remaining casing fixes completed!"
